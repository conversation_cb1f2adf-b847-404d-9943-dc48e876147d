//
//  SubscriptionService.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import Foundation
import RevenueCat
import Combine

/**
 * 订阅服务
 * 提供订阅相关的核心业务逻辑和数据管理
 * 与RevenueCat SDK集成，处理订阅状态同步
 */
@MainActor
class SubscriptionService: ObservableObject {
    
    // MARK: - Shared Instance
    static let shared = SubscriptionService()
    
    // MARK: - Published Properties
    @Published var currentSubscriptionLevel: RevenueCatManager.SubscriptionLevel = .free
    @Published var expirationDate: Date?
    @Published var customerInfo: CustomerInfo?
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let revenueCatManager = RevenueCatManager.shared
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Product IDs
    struct ProductIDs {
        static let basicMonthly = "com.ztt2.subscription.monthly.basic"
        static let basicYearly = "com.ztt2.subscription.yearly.basic"
        static let premiumMonthly = "com.ztt2.subscription.monthly.premium"
        static let premiumYearly = "com.ztt2.subscription.yearly.premium"
        
        static let allProducts = [basicMonthly, basicYearly, premiumMonthly, premiumYearly]
    }
    
    // MARK: - Initialization
    private init() {
        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /**
     * 配置订阅服务
     */
    func configure(userId: String? = nil) {
        // RevenueCat已在App启动时配置，这里主要是同步状态
        if let userId = userId {
            identify(userId: userId)
        }
        
        // 从RevenueCat获取订阅数据
        loadSubscriptionFromRevenueCat()
    }
    
    /**
     * 设置用户标识
     */
    func identify(userId: String) {
        isLoading = true
        
        Task {
            do {
                let (customerInfo, created) = try await Purchases.shared.logIn(userId)
                
                await MainActor.run {
                    self.customerInfo = customerInfo
                    self.isLoading = false
                    print("✅ 用户标识设置成功: \(userId), 新用户: \(created)")
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    print("❌ 用户标识设置失败: \(error)")
                }
            }
        }
    }
    
    /**
     * 购买订阅产品
     */
    func purchaseProduct(productId: String) async -> Bool {
        isLoading = true
        let success = await revenueCatManager.purchaseProduct(productId: productId)
        isLoading = false
        return success
    }
    
    /**
     * 恢复购买
     */
    func restorePurchases() async -> Bool {
        isLoading = true
        let success = await revenueCatManager.restorePurchases()
        isLoading = false
        return success
    }
    
    /**
     * 检查用户是否有特定权限
     */
    func hasPermission(for feature: SubscriptionFeature) -> Bool {
        switch feature {
        case .cloudSync:
            return currentSubscriptionLevel.hasCloudSync
        case .aiAnalysis:
            return currentSubscriptionLevel.hasAIAnalysis
        case .basicLottery:
            return currentSubscriptionLevel.hasAdvancedLottery
        case .advancedLottery:
            return currentSubscriptionLevel == .premium
        case .unlimitedMembers:
            return currentSubscriptionLevel != .free
        }
    }
    
    /**
     * 获取用户可创建的最大成员数
     */
    func getMaxMembers() -> Int {
        return currentSubscriptionLevel.maxMembers
    }
    
    /**
     * 检查是否为付费用户
     */
    func isPaidUser() -> Bool {
        return currentSubscriptionLevel != .free
    }
    
    /**
     * 检查是否为高级会员
     */
    func isPremiumUser() -> Bool {
        return currentSubscriptionLevel == .premium
    }
    
    /**
     * 获取订阅状态描述
     */
    func getSubscriptionStatusDescription() -> String {
        if currentSubscriptionLevel == .free {
            return "免费用户"
        }
        
        guard let expirationDate = expirationDate else {
            return currentSubscriptionLevel.displayName
        }
        
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        
        if expirationDate > Date() {
            return "\(currentSubscriptionLevel.displayName) - 到期时间: \(formatter.string(from: expirationDate))"
        } else {
            return "订阅已过期"
        }
    }
    
    /**
     * 获取当前订阅到期日期
     */
    func getCurrentExpirationDate() -> Date? {
        return revenueCatManager.expirationDate
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置观察者
     */
    private func setupObservers() {
        revenueCatManager.$currentSubscriptionLevel
            .sink { [weak self] level in
                self?.currentSubscriptionLevel = level
                self?.syncSubscriptionToDataManager()
            }
            .store(in: &cancellables)
        
        revenueCatManager.$customerInfo
            .sink { [weak self] customerInfo in
                self?.customerInfo = customerInfo
            }
            .store(in: &cancellables)
        
        revenueCatManager.$expirationDate
            .sink { [weak self] date in
                self?.expirationDate = date
            }
            .store(in: &cancellables)
        
        revenueCatManager.$isLoading
            .sink { [weak self] loading in
                self?.isLoading = loading
            }
            .store(in: &cancellables)
        
        revenueCatManager.$errorMessage
            .sink { [weak self] error in
                self?.errorMessage = error
            }
            .store(in: &cancellables)
    }
    
    /**
     * 从RevenueCat加载订阅数据
     */
    private func loadSubscriptionFromRevenueCat() {
        currentSubscriptionLevel = revenueCatManager.currentSubscriptionLevel
        expirationDate = revenueCatManager.expirationDate
        customerInfo = revenueCatManager.customerInfo
    }
    
    /**
     * 同步订阅状态到DataManager
     */
    private func syncSubscriptionToDataManager() {
        guard let user = dataManager.currentUser else { return }
        
        let oldLevel = user.subscriptionType ?? "free"
        let newLevel = currentSubscriptionLevel.rawValue
        
        // 更新用户订阅类型
        user.subscriptionType = newLevel
        
        // 更新或创建订阅记录
        if user.subscription == nil && currentSubscriptionLevel != .free {
            let newSubscription = Subscription(context: dataManager.viewContext)
            newSubscription.level = newLevel
            newSubscription.startDate = Date()
            newSubscription.endDate = expirationDate
            newSubscription.isActive = true
            newSubscription.user = user
        } else if let subscription = user.subscription {
            subscription.level = newLevel
            subscription.endDate = expirationDate
            subscription.isActive = currentSubscriptionLevel != .free
        }
        
        // 保存更改
        dataManager.saveContext()
        
        // 如果订阅等级发生变化，处理相关逻辑
        if oldLevel != newLevel {
            handleSubscriptionLevelChange(oldLevel: oldLevel, newLevel: newLevel)
        }
    }
    
    /**
     * 处理订阅等级变化
     */
    private func handleSubscriptionLevelChange(oldLevel: String, newLevel: String) {
        print("📱 订阅等级变化: \(oldLevel) -> \(newLevel)")
        
        // 发送订阅状态变化通知
        NotificationCenter.default.post(name: NSNotification.Name("SubscriptionStatusChanged"), object: nil)
        
        // 如果从免费升级到付费，启用CloudKit同步
        if oldLevel == "free" && newLevel != "free" {
            enableCloudKitSync()
        }
        
        // 如果从付费降级到免费，禁用CloudKit同步
        if oldLevel != "free" && newLevel == "free" {
            disableCloudKitSync()
        }
    }
    
    /**
     * 启用CloudKit同步
     */
    private func enableCloudKitSync() {
        // 这里可以添加启用CloudKit同步的逻辑
        print("🔄 启用CloudKit同步")
        NotificationCenter.default.post(name: NSNotification.Name("EnableCloudKitSync"), object: nil)
    }
    
    /**
     * 禁用CloudKit同步
     */
    private func disableCloudKitSync() {
        // 这里可以添加禁用CloudKit同步的逻辑
        print("⏸️ 禁用CloudKit同步")
        NotificationCenter.default.post(name: NSNotification.Name("DisableCloudKitSync"), object: nil)
    }
}

// MARK: - Subscription Features

enum SubscriptionFeature {
    case cloudSync          // 多设备同步
    case aiAnalysis         // AI分析
    case basicLottery       // 基础抽奖（大转盘）
    case advancedLottery    // 高级抽奖（盲盒、刮刮卡）
    case unlimitedMembers   // 更多成员
}

// MARK: - Extensions

extension RevenueCatManager.SubscriptionLevel {
    var localizedDisplayName: String {
        switch self {
        case .free:
            return "subscription.level.free".localized
        case .basic:
            return "subscription.level.basic".localized
        case .premium:
            return "subscription.level.premium".localized
        }
    }
}

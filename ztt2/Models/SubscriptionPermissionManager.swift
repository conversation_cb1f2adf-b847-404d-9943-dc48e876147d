//
//  SubscriptionPermissionManager.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import Foundation
import SwiftUI

/**
 * 订阅权限管理器
 * 提供统一的权限检查接口，用于控制应用功能的访问权限
 */
class SubscriptionPermissionManager {
    
    // MARK: - Shared Instance
    static let shared = SubscriptionPermissionManager()
    
    private init() {}
    
    // MARK: - Permission Check Methods
    
    /**
     * 检查是否可以使用多设备同步功能
     */
    func canUseCloudSync() -> Bool {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.hasPermission(for: .cloudSync)
    }
    
    /**
     * 检查是否可以使用AI分析功能
     */
    func canUseAIAnalysis() -> Bool {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.hasPermission(for: .aiAnalysis)
    }
    
    /**
     * 检查是否可以使用大转盘抽奖
     */
    func canUseBasicLottery() -> Bool {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.hasPermission(for: .basicLottery)
    }
    
    /**
     * 检查是否可以使用高级抽奖（盲盒、刮刮卡）
     */
    func canUseAdvancedLottery() -> Bool {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.hasPermission(for: .advancedLottery)
    }
    
    /**
     * 检查是否可以创建更多成员
     */
    func canCreateMoreMembers(currentCount: Int) -> Bool {
        let subscriptionService = SubscriptionService.shared
        let maxMembers = subscriptionService.getMaxMembers()
        return currentCount < maxMembers
    }
    
    /**
     * 获取最大成员数限制
     */
    func getMaxMembersLimit() -> Int {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.getMaxMembers()
    }
    
    /**
     * 检查是否为付费用户
     */
    func isPaidUser() -> Bool {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.isPaidUser()
    }
    
    /**
     * 检查是否为高级会员
     */
    func isPremiumUser() -> Bool {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.isPremiumUser()
    }
    
    /**
     * 获取当前订阅等级描述
     */
    func getCurrentSubscriptionDescription() -> String {
        let subscriptionService = SubscriptionService.shared
        return subscriptionService.getSubscriptionStatusDescription()
    }
    
    /**
     * 显示权限不足提示并引导订阅
     */
    func showPermissionDeniedAlert(for feature: SubscriptionFeature, from viewController: UIViewController? = nil) {
        let title = "权限不足"
        let message = getPermissionDeniedMessage(for: feature)
        
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        
        // 升级按钮
        alert.addAction(UIAlertAction(title: "立即升级", style: .default) { _ in
            self.presentSubscriptionView()
        })
        
        // 取消按钮
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 显示弹窗
        if let viewController = viewController {
            viewController.present(alert, animated: true)
        } else if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first,
                  let rootViewController = window.rootViewController {
            rootViewController.present(alert, animated: true)
        }
    }
    
    // MARK: - Private Helper Methods
    
    /**
     * 获取权限不足的提示信息
     */
    private func getPermissionDeniedMessage(for feature: SubscriptionFeature) -> String {
        switch feature {
        case .cloudSync:
            return "多设备同步功能需要升级到初级会员或高级会员才能使用。升级后，您的数据将在所有设备间自动同步。"
        case .aiAnalysis:
            return "AI分析功能需要升级到高级会员才能使用。升级后，您可以获得个性化的行为分析报告和成长建议。"
        case .basicLottery:
            return "大转盘抽奖功能需要升级到初级会员或高级会员才能使用。升级后，您可以为孩子配置有趣的抽奖奖品。"
        case .advancedLottery:
            return "盲盒和刮刮卡功能需要升级到高级会员才能使用。升级后，您可以使用所有抽奖道具，让积分兑换更有趣。"
        case .unlimitedMembers:
            return "创建更多家庭成员需要升级会员。免费用户最多可创建10个成员，初级会员20个，高级会员50个。"
        }
    }
    
    /**
     * 显示订阅页面
     */
    private func presentSubscriptionView() {
        // 发送显示订阅页面的通知
        NotificationCenter.default.post(name: .showSubscriptionView, object: nil)
    }
}

// MARK: - SwiftUI View Extensions

extension View {
    /**
     * 检查权限并在权限不足时显示订阅引导
     */
    func requiresPermission(for feature: SubscriptionFeature, action: @escaping () -> Void) -> some View {
        self.onTapGesture {
            if SubscriptionPermissionManager.shared.hasPermission(for: feature) {
                action()
            } else {
                SubscriptionPermissionManager.shared.showPermissionDeniedAlert(for: feature)
            }
        }
    }
    
    /**
     * 根据权限状态显示不同的视图
     */
    func conditionalByPermission<Content: View>(
        for feature: SubscriptionFeature,
        @ViewBuilder content: () -> Content
    ) -> some View {
        Group {
            if SubscriptionPermissionManager.shared.hasPermission(for: feature) {
                content()
            } else {
                self
            }
        }
    }
}

// MARK: - Permission Helper Extension

extension SubscriptionPermissionManager {
    /**
     * 统一的权限检查方法
     */
    func hasPermission(for feature: SubscriptionFeature) -> Bool {
        switch feature {
        case .cloudSync:
            return canUseCloudSync()
        case .aiAnalysis:
            return canUseAIAnalysis()
        case .basicLottery:
            return canUseBasicLottery()
        case .advancedLottery:
            return canUseAdvancedLottery()
        case .unlimitedMembers:
            return isPaidUser()
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let showSubscriptionView = Notification.Name("showSubscriptionView")
}

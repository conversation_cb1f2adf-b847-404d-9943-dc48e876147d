//
//  SubscriptionPermissionExamples.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/4.
//

import SwiftUI

/**
 * 订阅权限使用示例
 * 展示如何在应用的各个功能中集成订阅权限检查
 */

// MARK: - 示例1：AI分析功能权限检查

struct AIAnalysisButtonExample: View {
    @StateObject private var permissionManager = SubscriptionPermissionManager.shared
    
    var body: some View {
        Button("生成AI分析报告") {
            if permissionManager.canUseAIAnalysis() {
                // 执行AI分析
                generateAIAnalysis()
            } else {
                // 显示权限不足提示
                showUpgradePrompt(for: .aiAnalysis)
            }
        }
        .foregroundColor(permissionManager.canUseAIAnalysis() ? .blue : .gray)
        .disabled(!permissionManager.canUseAIAnalysis())
    }
    
    private func generateAIAnalysis() {
        // AI分析逻辑
        print("开始生成AI分析报告")
    }
    
    private func showUpgradePrompt(for feature: SubscriptionFeature) {
        // 显示升级提示
        print("需要升级到高级会员才能使用AI分析功能")
    }
}

// MARK: - 示例2：抽奖功能权限检查

struct LotteryOptionsExample: View {
    @StateObject private var permissionManager = SubscriptionPermissionManager.shared
    
    var body: some View {
        VStack(spacing: 16) {
            // 大转盘（初级会员及以上）
            LotteryOptionCard(
                title: "大转盘",
                description: "经典转盘抽奖",
                isAvailable: permissionManager.canUseBasicLottery(),
                requiredLevel: "初级会员"
            ) {
                if permissionManager.canUseBasicLottery() {
                    openWheelLottery()
                } else {
                    showUpgradePrompt(for: .basicLottery)
                }
            }
            
            // 盲盒（高级会员）
            LotteryOptionCard(
                title: "盲盒",
                description: "神秘盲盒抽奖",
                isAvailable: permissionManager.canUseAdvancedLottery(),
                requiredLevel: "高级会员"
            ) {
                if permissionManager.canUseAdvancedLottery() {
                    openBlindBox()
                } else {
                    showUpgradePrompt(for: .advancedLottery)
                }
            }
            
            // 刮刮卡（高级会员）
            LotteryOptionCard(
                title: "刮刮卡",
                description: "刮开惊喜奖品",
                isAvailable: permissionManager.canUseAdvancedLottery(),
                requiredLevel: "高级会员"
            ) {
                if permissionManager.canUseAdvancedLottery() {
                    openScratchCard()
                } else {
                    showUpgradePrompt(for: .advancedLottery)
                }
            }
        }
    }
    
    private func openWheelLottery() {
        print("打开大转盘")
    }
    
    private func openBlindBox() {
        print("打开盲盒")
    }
    
    private func openScratchCard() {
        print("打开刮刮卡")
    }
    
    private func showUpgradePrompt(for feature: SubscriptionFeature) {
        SubscriptionPermissionManager.shared.showPermissionDeniedAlert(for: feature)
    }
}

struct LotteryOptionCard: View {
    let title: String
    let description: String
    let isAvailable: Bool
    let requiredLevel: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(isAvailable ? .primary : .gray)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if !isAvailable {
                        Text("需要\(requiredLevel)")
                            .font(.caption2)
                            .foregroundColor(.orange)
                    }
                }
                
                Spacer()
                
                Image(systemName: isAvailable ? "checkmark.circle.fill" : "lock.circle.fill")
                    .foregroundColor(isAvailable ? .green : .gray)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .disabled(!isAvailable)
    }
}

// MARK: - 示例3：成员创建限制检查

struct AddMemberButtonExample: View {
    @StateObject private var permissionManager = SubscriptionPermissionManager.shared
    @State private var currentMemberCount: Int = 5 // 示例：当前已有5个成员
    
    var body: some View {
        VStack(spacing: 8) {
            Button("添加家庭成员") {
                if permissionManager.canCreateMoreMembers(currentCount: currentMemberCount) {
                    addNewMember()
                } else {
                    showMemberLimitAlert()
                }
            }
            .disabled(!permissionManager.canCreateMoreMembers(currentCount: currentMemberCount))
            
            // 显示成员数量限制
            Text("成员数量：\(currentMemberCount)/\(permissionManager.getMaxMembersLimit())")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func addNewMember() {
        // 添加成员逻辑
        currentMemberCount += 1
        print("添加新成员，当前成员数：\(currentMemberCount)")
    }
    
    private func showMemberLimitAlert() {
        SubscriptionPermissionManager.shared.showPermissionDeniedAlert(for: .unlimitedMembers)
    }
}

// MARK: - 示例4：iCloud同步设置

struct CloudSyncSettingExample: View {
    @StateObject private var permissionManager = SubscriptionPermissionManager.shared
    @State private var isCloudSyncEnabled: Bool = false
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text("iCloud同步")
                    .font(.headline)
                
                Text("在多个设备间同步数据")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if !permissionManager.canUseCloudSync() {
                    Text("需要初级会员或高级会员")
                        .font(.caption2)
                        .foregroundColor(.orange)
                }
            }
            
            Spacer()
            
            Toggle("", isOn: $isCloudSyncEnabled)
                .disabled(!permissionManager.canUseCloudSync())
                .onChange(of: isCloudSyncEnabled) { newValue in
                    if newValue && !permissionManager.canUseCloudSync() {
                        // 重置开关状态
                        isCloudSyncEnabled = false
                        // 显示升级提示
                        SubscriptionPermissionManager.shared.showPermissionDeniedAlert(for: .cloudSync)
                    } else {
                        // 执行同步设置
                        updateCloudSyncSetting(enabled: newValue)
                    }
                }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func updateCloudSyncSetting(enabled: Bool) {
        print("iCloud同步设置更新：\(enabled)")
        // 实际的同步设置逻辑
    }
}

// MARK: - 示例5：订阅状态显示

struct SubscriptionStatusExample: View {
    @StateObject private var permissionManager = SubscriptionPermissionManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("订阅状态")
                .font(.headline)
            
            HStack {
                Image(systemName: permissionManager.isPaidUser() ? "crown.fill" : "person.circle")
                    .foregroundColor(permissionManager.isPaidUser() ? .yellow : .gray)
                
                VStack(alignment: .leading) {
                    Text(permissionManager.getCurrentSubscriptionDescription())
                        .font(.subheadline)
                    
                    if !permissionManager.isPaidUser() {
                        Button("升级会员") {
                            showSubscriptionView()
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
            }
            
            // 功能权限列表
            VStack(alignment: .leading, spacing: 4) {
                PermissionRow(title: "多设备同步", hasPermission: permissionManager.canUseCloudSync())
                PermissionRow(title: "AI分析", hasPermission: permissionManager.canUseAIAnalysis())
                PermissionRow(title: "大转盘抽奖", hasPermission: permissionManager.canUseBasicLottery())
                PermissionRow(title: "盲盒&刮刮卡", hasPermission: permissionManager.canUseAdvancedLottery())
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private func showSubscriptionView() {
        // 显示订阅页面
        NotificationCenter.default.post(name: .showSubscriptionView, object: nil)
    }
}

struct PermissionRow: View {
    let title: String
    let hasPermission: Bool
    
    var body: some View {
        HStack {
            Image(systemName: hasPermission ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(hasPermission ? .green : .red)
                .font(.caption)
            
            Text(title)
                .font(.caption)
                .foregroundColor(hasPermission ? .primary : .secondary)
            
            Spacer()
        }
    }
}

// MARK: - 预览

#Preview {
    VStack(spacing: 20) {
        AIAnalysisButtonExample()
        AddMemberButtonExample()
        CloudSyncSettingExample()
        SubscriptionStatusExample()
    }
    .padding()
}

//
//  SimpleUserInfoTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/4.
//

import SwiftUI

/**
 * 简单的用户信息测试视图
 * 用于快速验证个人中心和订阅页面的用户信息是否一致
 */
struct SimpleUserInfoTestView: View {
    
    // MARK: - State Objects
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    
    // MARK: - Environment
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题
                    Text("用户信息一致性检查")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding()
                    
                    // 个人中心信息
                    InfoSection(
                        title: "个人中心页面信息",
                        userName: profileUserName,
                        userID: profileUserID,
                        membershipLevel: profileMembershipLevel,
                        expirationDate: profileExpirationDate,
                        backgroundColor: Color.blue.opacity(0.1)
                    )
                    
                    // 订阅页面信息
                    InfoSection(
                        title: "订阅页面信息",
                        userName: subscriptionUserName,
                        userID: subscriptionUserID,
                        membershipLevel: subscriptionMembershipLevel,
                        expirationDate: subscriptionExpirationDate,
                        backgroundColor: Color.green.opacity(0.1)
                    )
                    
                    // 一致性状态
                    ConsistencyStatus(
                        userNameMatch: profileUserName == subscriptionUserName,
                        userIDMatch: profileUserID == subscriptionUserID,
                        membershipLevelMatch: profileMembershipLevel == subscriptionMembershipLevel,
                        expirationDateMatch: profileExpirationDate == subscriptionExpirationDate
                    )
                    
                    // 刷新按钮
                    Button("刷新数据") {
                        refreshData()
                    }
                    .buttonStyle(.borderedProminent)
                    .padding()
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("一致性测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            refreshData()
        }
    }
    
    // MARK: - Computed Properties (模拟ProfileView的逻辑)
    
    private var profileUserName: String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }
    
    private var profileUserID: String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }
    
    private var profileMembershipLevel: String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }
    
    private var profileExpirationDate: String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }
    
    // MARK: - Computed Properties (模拟SubscriptionView的逻辑)
    
    private var subscriptionUserName: String {
        return dataManager.currentUser?.nickname ?? "user_info.parent_nickname".localized
    }
    
    private var subscriptionUserID: String {
        return authManager.currentUser?.email ?? "profile.no_email".localized
    }
    
    private var subscriptionMembershipLevel: String {
        guard let user = dataManager.currentUser else {
            return "subscription.user_level_regular".localized
        }

        switch user.subscriptionType {
        case "premium":
            return "高级会员"
        case "basic":
            return "初级会员"
        default:
            return "免费用户"
        }
    }
    
    private var subscriptionExpirationDate: String {
        guard let user = dataManager.currentUser,
              let subscription = user.subscription,
              subscription.isActive,
              let endDate = subscription.endDate else {
            return "subscription.not_activated".localized
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: endDate)
    }
    
    // MARK: - Private Methods
    
    private func refreshData() {
        dataManager.refreshCurrentUser()
        print("🔄 刷新用户数据完成")
    }
}

// MARK: - Supporting Views

/**
 * 信息区域组件
 */
struct InfoSection: View {
    let title: String
    let userName: String
    let userID: String
    let membershipLevel: String
    let expirationDate: String
    let backgroundColor: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                InfoItem(label: "用户名", value: userName)
                InfoItem(label: "用户ID", value: userID)
                InfoItem(label: "会员等级", value: membershipLevel)
                InfoItem(label: "到期日期", value: expirationDate)
            }
        }
        .padding()
        .background(backgroundColor)
        .cornerRadius(12)
    }
}

/**
 * 信息项组件
 */
struct InfoItem: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text("\(label):")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
            
            Spacer()
        }
    }
}

/**
 * 一致性状态组件
 */
struct ConsistencyStatus: View {
    let userNameMatch: Bool
    let userIDMatch: Bool
    let membershipLevelMatch: Bool
    let expirationDateMatch: Bool
    
    private var allMatch: Bool {
        userNameMatch && userIDMatch && membershipLevelMatch && expirationDateMatch
    }
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: allMatch ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                    .foregroundColor(allMatch ? .green : .orange)
                    .font(.title2)
                
                Text(allMatch ? "✅ 所有信息一致" : "⚠️ 发现不一致")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(allMatch ? .green : .orange)
                
                Spacer()
            }
            
            VStack(spacing: 6) {
                StatusRow(label: "用户名", isMatch: userNameMatch)
                StatusRow(label: "用户ID", isMatch: userIDMatch)
                StatusRow(label: "会员等级", isMatch: membershipLevelMatch)
                StatusRow(label: "到期日期", isMatch: expirationDateMatch)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

/**
 * 状态行组件
 */
struct StatusRow: View {
    let label: String
    let isMatch: Bool
    
    var body: some View {
        HStack {
            Image(systemName: isMatch ? "checkmark.circle" : "xmark.circle")
                .foregroundColor(isMatch ? .green : .red)
            
            Text(label)
                .font(.body)
            
            Spacer()
            
            Text(isMatch ? "一致" : "不一致")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(isMatch ? .green : .red)
        }
    }
}

// MARK: - Preview

#Preview {
    SimpleUserInfoTestView()
}
